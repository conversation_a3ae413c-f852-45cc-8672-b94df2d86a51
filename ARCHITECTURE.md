# 智能文档系统架构设计

## 1. 概述

本文档基于详细的需求说明，旨在为智能文档项目设计一个高性能、高可用、可扩展的系统架构。

项目核心挑战在于：
- **富文本编辑与实时协作**：支持多人毫秒级延迟的同步编辑。
- **复杂的智能功能**：深度集成AI能力，提供写作、改写、校对等多种智能服务。
- **高性能与高并发**：确保系统在多人使用时依然保持流畅的用户体验。

基于以上挑战，我们选择**微服务架构**，将系统按功能域划分为独立的服务，以便于开发、部署和扩展。

## 2. 架构图

```
+----------------------+      +---------------------+      +--------------------+
|                      |      |                     |      |   用户服务         |
|   前端应用 (React)    |----->|    API 网关         |----->|  (User Service)    |
| (TipTap Editor)      |      |  (Node.js/Express)  |      +--------------------+
|                      |      |                     |               |
+----------+-----------+      +----------+----------+      +--------------------+
           |                             |                 |   文档服务         |
           | (WebSocket)                 |---------------->| (Document Service) |
           |                             |                 +--------------------+
           |                             |                 |
+----------v-----------+      +----------v----------+      +--------------------+
|  实时协作服务        +----->|     AI 服务         |<---->|   大语言模型(LLM)  |
| (Y.js + WebSocket)   |      |  (Python/FastAPI)   |      |   (Gemini API)     |
+----------------------+      +---------------------+      +--------------------+

+---------------------------+      +--------------------+
|                           |<-----|   用户服务         |
|     PostgreSQL 数据库      |      +--------------------+
| (用户, 文档, 权限, 版本)  |<-----|   文档服务         |
|                           |      +--------------------+
+---------------------------+      +--------------------+
                                   |   导入/导出服务    |
+---------------------------+      | (Pandoc/LibreOffice)|
|      对象存储 (S3)        |<---->+--------------------+
| (图片, 导出文件)          |
+---------------------------+      +--------------------+
                                   |   实时协作服务     |----->+  Redis 缓存/消息队列  |
                                   +--------------------+      +--------------------+
```

## 3. 核心组件与技术选型

### 3.1. 前端应用 (Client)

- **职责**: 提供用户界面，包括文档编辑器、聊天窗口、文件管理等。
- **技术栈**:
    - **框架**: **React**。利用其组件化和庞大的生态系统。
    - **富文本编辑器**: **TipTap (基于 Prosemirror)**。它是一个无头(headless)编辑器框架，专为构建现代、可扩展和协作式的编辑器而设计。将通过扩展支持 Markdown 实时渲染。
    - **实时通信**: 原生 **WebSocket** 或 **Socket.IO-client**，与实时协作服务连接。
    - **状态管理**: **Redux Toolkit** 或 **Zustand**，用于管理复杂的应用状态。
    - **UI 库**: **Material-UI (MUI)**，遵循现代设计语言，提供丰富的组件。

### 3.2. API 网关 (API Gateway)

- **职责**: 作为系统的统一入口，处理路由、认证(JWT)、限流和日志记录。
- **技术栈**: **Node.js + Express.js** 或专用网关服务（如 Kong, Traefik）。

### 3.3. 核心后端服务

#### a. 用户服务
- **职责**: 管理用户账户、认证、授权和个人信息。
- **技术栈**: **Node.js + Express.js**。

#### b. 文档服务
- **职责**: 负责文档的CRUD、权限管理、版本历史、评论、文件夹系统和模板管理。
- **技术栈**: **Node.js + Express.js**。

#### c. 导入/导出服务
- **职责**: 处理 `.docx`, `.pdf`, `.md`, `.txt` 等格式的转换。这是一个计算密集型任务，适合独立部署。
- **技术栈**: 可以使用 **Pandoc** 或 **LibreOffice** 的命令行工具，通过一个简单的Web服务包装。

### 3.4. 实时协作服务

- **职责**: 处理文档的实时协同编辑。这是保证协作体验的核心。
- **技术栈**:
    - **核心算法**: **CRDTs (Conflict-free Replicated Data Types)**。相比传统的OT算法，CRDTs在实现多用户协作时更简单、更鲁棒。
    - **实现**: **Y.js**。这是一个成熟的CRDTs实现库，提供了开箱即用的前后端解决方案。
    - **通信协议**: **WebSocket**，用于在客户端和服务端之间建立持久连接，实时双向通信。
    - **后端**: **Node.js** 服务器，运行 Y.js 的 WebSocket Provider。

### 3.5. AI 服务

- **职责**: 封装所有与大语言模型（LLM）的交互，提供统一的智能功能接口。
- **技术栈**:
    - **框架**: **Python + FastAPI**。FastAPI性能卓越，且Python是AI/ML领域的首选语言。
    - **接口**: 提供RESTful API，如 `/rewrite`, `/summarize`, `/chat-command` 等，供API网关调用。
    - **交互**: 调用 **Gemini API** 或其他LLM服务来执行具体的AI任务。

## 4. 数据存储

### 4.1. 主数据库

- **选型**: **PostgreSQL**。
- **理由**:
    - 强大的关系型数据管理能力，适合存储用户、权限、文件夹等结构化数据。
    - **JSONB** 数据类型非常适合存储半结构化的文档内容、评论和版本差异(diffs)。
    - 事务支持保证了数据一致性。

### 4.2. 缓存/消息队列

- **选型**: **Redis**。
- **理由**:
    - **缓存**: 缓存用户会话、热点文档数据，降低数据库压力。
    - **消息队列 (Pub/Sub)**: 当实时协作服务需要水平扩展时，可使用Redis Pub/Sub在多个实例间广播消息。

### 4.3. 对象存储

- **选型**: **AWS S3** 或兼容的开源方案如 **MinIO**。
- **理由**: 专门用于存储二进制文件，如用户上传的图片、导出的PDF/DOCX文件。成本低、可扩展性强。

## 5. 总结

该架构将业务逻辑清晰地划分到不同的服务中，实现了关注点分离。
- **前端**负责用户体验。
- **核心后端**处理业务规则。
- **协作服务**和**AI服务**作为专门的引擎，处理最复杂的计算任务。

这种设计不仅满足了当前所有的功能和非功能需求，也为未来的功能扩展和性能优化提供了坚实的基础。
